{"compilerOptions": {"target": "ESNext", "useDefineForClassFields": true, "module": "ESNext", "moduleResolution": "node", "allowImportingTsExtensions": true, "strict": true, "jsx": "preserve", "resolveJsonModule": true, "isolatedModules": true, "esModuleInterop": true, "lib": ["ESNext", "DOM"], "skipLibCheck": true, "noEmit": true, "allowSyntheticDefaultImports": true, "types": ["vite/client", "node"]}, "include": ["src/**/*", "src/**/*.vue", "src/**/*.d.ts"], "references": [{"path": "./tsconfig.node.json"}]}